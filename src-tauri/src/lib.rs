use anyhow::Result;
use reqwest::multipart;
use serde::{Deserialize, Serialize};
use std::path::Path;

// 阿里云上传凭证响应结构
#[derive(Debug, Deserialize)]
struct UploadPolicyResponse {
    data: UploadPolicyData,
}

#[derive(Debug, Deserialize)]
struct UploadPolicyData {
    policy: String,
    signature: String,
    upload_dir: String,
    upload_host: String,
    expire_in_seconds: u32,
    max_file_size_mb: u32,
    oss_access_key_id: String,
    x_oss_object_acl: String,
    x_oss_forbid_overwrite: String,
}

// 图像编辑任务创建响应
#[derive(Debug, Deserialize)]
struct ImageEditTaskResponse {
    output: TaskOutput,
    request_id: String,
}

#[derive(Debug, Deserialize)]
struct TaskOutput {
    task_id: String,
    task_status: String,
}

// 图像编辑任务查询响应
#[derive(Debug, Serialize, Deserialize)]
struct ImageEditResultResponse {
    output: TaskResultOutput,
    request_id: String,
    usage: Option<Usage>,
}

#[derive(Debug, Serialize, Deserialize)]
struct TaskResultOutput {
    task_id: String,
    task_status: String,
    results: Option<Vec<TaskResult>>,
    submit_time: Option<String>,
    scheduled_time: Option<String>,
    end_time: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
struct TaskResult {
    url: Option<String>,
    code: Option<String>,
    message: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
struct Usage {
    image_count: u32,
}

// 前端传递的参数结构
#[derive(Debug, Serialize, Deserialize)]
struct UploadFileRequest {
    file_path: String,
    model_name: String,
}

#[derive(Debug, Serialize, Deserialize)]
struct ImageEditRequest {
    image_url: String,
    function: String,
    prompt: String,
    mask_image_url: Option<String>,
    parameters: Option<serde_json::Value>,
}

// 获取阿里云上传凭证
async fn get_upload_policy(api_key: &str, model_name: &str) -> Result<UploadPolicyData> {
    let client = reqwest::Client::new();
    let url = "https://dashscope.aliyuncs.com/api/v1/uploads";

    let response = client
        .get(url)
        .header("Authorization", format!("Bearer {}", api_key))
        .header("Content-Type", "application/json")
        .query(&[("action", "getPolicy"), ("model", model_name)])
        .send()
        .await?;

    let status = response.status();
    let response_text = response.text().await?;

    if !status.is_success() {
        return Err(anyhow::anyhow!("API returned error status {}: {}", status, response_text));
    }

    // 打印响应内容用于调试
    println!("Upload policy response: {}", response_text);

    // 尝试解析JSON
    let policy_response: UploadPolicyResponse = serde_json::from_str(&response_text)
        .map_err(|e| anyhow::anyhow!("Failed to parse JSON response: {}. Response was: {}", e, response_text))?;

    Ok(policy_response.data)
}

// 上传文件到OSS
async fn upload_file_to_oss(policy_data: &UploadPolicyData, file_path: &str) -> Result<String> {
    let file_name = Path::new(file_path)
        .file_name()
        .and_then(|name| name.to_str())
        .ok_or_else(|| anyhow::anyhow!("Invalid file path"))?;

    let key = format!("{}/{}", policy_data.upload_dir, file_name);

    // 读取文件内容
    let file_content = tokio::fs::read(file_path).await?;

    // 创建multipart表单
    let form = multipart::Form::new()
        .text("OSSAccessKeyId", policy_data.oss_access_key_id.clone())
        .text("Signature", policy_data.signature.clone())
        .text("policy", policy_data.policy.clone())
        .text("x-oss-object-acl", policy_data.x_oss_object_acl.clone())
        .text("x-oss-forbid-overwrite", policy_data.x_oss_forbid_overwrite.clone())
        .text("key", key.clone())
        .text("success_action_status", "200")
        .part("file", multipart::Part::bytes(file_content).file_name(file_name.to_string()));

    let client = reqwest::Client::new();
    let response = client
        .post(&policy_data.upload_host)
        .multipart(form)
        .send()
        .await?;

    if !response.status().is_success() {
        return Err(anyhow::anyhow!("Failed to upload file: {}", response.text().await?));
    }

    Ok(format!("oss://{}", key))
}

// Tauri命令：上传文件并获取URL
#[tauri::command]
async fn upload_file_and_get_url(api_key: String, model_name: String, file_path: String) -> Result<String, String> {
    println!("开始上传文件: {}", file_path);
    println!("使用API Key: {}...", &api_key[..std::cmp::min(10, api_key.len())]);
    println!("模型名称: {}", model_name);

    // 获取上传凭证
    let policy_data = get_upload_policy(&api_key, &model_name)
        .await
        .map_err(|e| format!("Failed to get upload policy: {}", e))?;

    println!("成功获取上传凭证");

    // 上传文件到OSS
    let oss_url = upload_file_to_oss(&policy_data, &file_path)
        .await
        .map_err(|e| format!("Failed to upload file: {}", e))?;

    println!("文件上传成功: {}", oss_url);
    Ok(oss_url)
}

// 创建图像编辑任务
async fn create_image_edit_task(
    api_key: &str,
    image_url: &str,
    function: &str,
    prompt: &str,
    mask_image_url: Option<&str>,
    parameters: Option<&serde_json::Value>,
) -> Result<String> {
    println!("开始创建图像编辑任务");
    println!("图片URL: {}", image_url);
    println!("功能: {}", function);
    println!("提示词: {}", prompt);

    let client = reqwest::Client::new();
    let url = "https://dashscope.aliyuncs.com/api/v1/services/aigc/image2image/image-synthesis";

    let mut input = serde_json::json!({
        "function": function,
        "prompt": prompt,
        "base_image_url": image_url
    });

    if let Some(mask_url) = mask_image_url {
        input["mask_image_url"] = serde_json::Value::String(mask_url.to_string());
        println!("遮罩图片URL: {}", mask_url);
    }

    let mut request_body = serde_json::json!({
        "model": "wanx2.1-imageedit",
        "input": input
    });

    if let Some(params) = parameters {
        request_body["parameters"] = params.clone();
    } else {
        request_body["parameters"] = serde_json::json!({"n": 1});
    }

    println!("请求体: {}", serde_json::to_string_pretty(&request_body).unwrap_or_default());

    let response = client
        .post(url)
        .header("Authorization", format!("Bearer {}", api_key))
        .header("Content-Type", "application/json")
        .header("X-DashScope-Async", "enable")
        .json(&request_body)
        .send()
        .await?;

    let status = response.status();
    let response_text = response.text().await?;

    println!("创建任务响应状态: {}", status);
    println!("创建任务响应内容: {}", response_text);

    if !status.is_success() {
        return Err(anyhow::anyhow!("Failed to create image edit task: {}", response_text));
    }

    let task_response: ImageEditTaskResponse = serde_json::from_str(&response_text)
        .map_err(|e| anyhow::anyhow!("Failed to parse task response: {}. Response was: {}", e, response_text))?;

    println!("任务创建成功，任务ID: {}", task_response.output.task_id);
    Ok(task_response.output.task_id)
}

// 查询图像编辑任务结果
async fn get_image_edit_result(api_key: &str, task_id: &str) -> Result<ImageEditResultResponse> {
    let client = reqwest::Client::new();
    let url = format!("https://dashscope.aliyuncs.com/api/v1/tasks/{}", task_id);

    let response = client
        .get(&url)
        .header("Authorization", format!("Bearer {}", api_key))
        .send()
        .await?;

    if !response.status().is_success() {
        return Err(anyhow::anyhow!("Failed to get task result: {}", response.text().await?));
    }

    let result: ImageEditResultResponse = response.json().await?;
    Ok(result)
}

// Tauri命令：创建图像编辑任务
#[tauri::command]
async fn create_image_edit(
    api_key: String,
    image_url: String,
    function: String,
    prompt: String,
    mask_image_url: Option<String>,
    parameters: Option<serde_json::Value>,
) -> Result<String, String> {
    let task_id = create_image_edit_task(
        &api_key,
        &image_url,
        &function,
        &prompt,
        mask_image_url.as_deref(),
        parameters.as_ref(),
    )
    .await
    .map_err(|e| format!("Failed to create image edit task: {}", e))?;

    Ok(task_id)
}

// Tauri命令：查询图像编辑任务结果
#[tauri::command]
async fn get_image_edit_task_result(api_key: String, task_id: String) -> Result<serde_json::Value, String> {
    let result = get_image_edit_result(&api_key, &task_id)
        .await
        .map_err(|e| format!("Failed to get task result: {}", e))?;

    Ok(serde_json::to_value(result).unwrap())
}

// Learn more about Tauri commands at https://tauri.app/develop/calling-rust/
#[tauri::command]
fn greet(name: &str) -> String {
    format!("Hello, {}! You've been greeted from Rust!", name)
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .plugin(tauri_plugin_dialog::init())
        .invoke_handler(tauri::generate_handler![
            greet,
            upload_file_and_get_url,
            create_image_edit,
            get_image_edit_task_result
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
